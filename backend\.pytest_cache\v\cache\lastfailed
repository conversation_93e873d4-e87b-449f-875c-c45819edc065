{"tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_delete_calculation_standard": true, "tests/test_api/test_document_routes.py::TestCalculationStandardRoutes::test_create_calculation_standard_duplicate_code": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_create_imported_data_revision": true, "tests/test_api/test_document_routes.py::TestImportedDataRevisionRoutes::test_list_imported_data_revisions_by_project": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_create_exported_document": true, "tests/test_api/test_document_routes.py::TestExportedDocumentRoutes::test_list_exported_documents_by_project": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success": true, "tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "tests/test_integration/test_simple_document_integration.py::TestDocumentIntegration::test_calculation_standard_service_operations": true, "tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "tests/test_services/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_list_switchboards_success": true, "tests/test_api/test_switchboard_api.py::TestSwitchboardAPI::test_business_logic_error_handling": true, "tests/test_api/test_user_api.py::TestUserManagementAPI::test_list_users_with_search": true, "tests/test_services/test_user_service.py::TestUserService::test_create_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_success": true, "tests/test_services/test_user_service.py::TestUserService::test_login_invalid_password": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_success": true, "tests/test_services/test_user_service.py::TestUserService::test_change_password_invalid_current": true, "tests/test_services/test_user_service.py::TestUserService::test_update_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_deactivate_user_success": true, "tests/test_services/test_user_service.py::TestUserService::test_get_user_preferences_success": true, "tests/test_services/test_user_service.py::TestUserService::test_create_or_update_user_preferences_success": true, "tests/test_services/test_user_service.py::TestUserService::test_search_users_success": true, "tests/test_services/test_user_service.py::TestUserService::test_hash_password": true, "tests/test_services/test_user_service.py::TestUserService::test_verify_password": true, "tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true}